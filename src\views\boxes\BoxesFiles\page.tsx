import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbBox, TbArrowLeft, TbFile, TbFileText } from 'react-icons/tb'
import { useGetBoxById } from '@/hooks/boxes'
import Button from '@/components/ui/Button'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import { BoxFile } from '@/@types/box'
import { useMemo } from 'react'
import Loading from '@/components/shared/Loading'
import Actions from '../Boxes/components/Actions'

const BoxFiles = () => {
    const { t } = useTranslation()
    const { boxId } = useParams<{ boxId: string }>()
    const navigate = useNavigate()

    const { data: boxDetails, isLoading, error } = useGetBoxById(boxId!)

    const tableColumns: ColumnDef<BoxFile>[] = useMemo(() => {
        const cols: ColumnDef<BoxFile>[] = [
            {
                header: t('nav.boxes.fileTitle'),
                accessorKey: 'fileTitle',
                cell: ({ row }) => (
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
                            <TbFile className="w-4 h-4 text-blue-600" />
                        </div>
                        <div className="font-medium text-gray-900">
                            {row.original.fileTitle}
                        </div>
                    </div>
                ),
            },
            {
                header: t('nav.boxes.documentsCount'),
                accessorKey: 'numberOfDocuments',
                cell: ({ row }) => (
                    <div className="text-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            {row.original.numberOfDocuments}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.boxes.pagesCount'),
                accessorKey: 'numberOfPages',
                cell: ({ row }) => (
                    <div className="text-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                            {row.original.numberOfPages}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.shared.actions'),
                accessorKey: 'action',
                cell: ({ row }) => (
                    <Actions
                        boxData={[] as never}
                        boxId={row.original.fileId}
                        boxView={true}
                    />
                ),
                enableSorting: false,
            },
        ]

        return cols
    }, [t])

    const handleBackToBoxes = () => {
        navigate('/boxes')
    }

    if (isLoading) {
        return (
            <Container>
                <Loading loading={true} />
            </Container>
        )
    }

    if (error || !boxDetails) {
        return (
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="text-center py-8">
                        <p className="text-red-600">
                            Error loading box details
                        </p>
                        <Button
                            className="mt-4"
                            icon={<TbArrowLeft />}
                            onClick={handleBackToBoxes}
                        >
                            {t('nav.boxes.backToBoxes')}
                        </Button>
                    </div>
                </AdaptiveCard>
            </Container>
        )
    }

    return (
        <Container>
            <AdaptiveCard className="shadow-lg">
                <div className="flex flex-col gap-6">
                    {/* Header Section */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                        <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                <TbBox className="w-5 h-5 text-primary-deep" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold">
                                    {t('nav.boxes.boxFiles')}
                                </h3>
                                <p className="text-sm text-gray-500">
                                    {t('nav.boxes.boxId')}: {boxDetails.boxId}
                                </p>
                            </div>
                        </div>
                        <Button
                            variant="default"
                            icon={<TbArrowLeft />}
                            onClick={handleBackToBoxes}
                        >
                            {t('nav.boxes.backToBoxes')}
                        </Button>
                    </div>

                    {/* Box Details Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4  rounded-lg">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                                {boxDetails.numberOfFiles}
                            </div>
                            <div className="text-sm text-gray-600">
                                {t('nav.boxes.filesCount')}
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                                {boxDetails.files?.reduce(
                                    (sum, file) => sum + file.numberOfDocuments,
                                    0,
                                ) || 0}
                            </div>
                            <div className="text-sm text-gray-600">
                                {t('nav.boxes.documentsCount')}
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                                {boxDetails.files?.reduce(
                                    (sum, file) => sum + file.numberOfPages,
                                    0,
                                ) || 0}
                            </div>
                            <div className="text-sm text-gray-600">
                                {t('nav.boxes.pagesCount')}
                            </div>
                        </div>
                    </div>

                    {/* Files Table */}
                    <div>
                        {boxDetails.files && boxDetails.files.length > 0 ? (
                            <DataTable
                                selectable
                                disablePageSize
                                columns={tableColumns}
                                data={boxDetails.files}
                                loading={false}
                                skeletonAvatarColumns={[0]}
                                skeletonAvatarProps={{ width: 14, height: 14 }}
                                cellBorder={true}
                            />
                        ) : (
                            <div className="text-center py-12">
                                <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4">
                                    <TbFileText className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    {t('nav.boxes.noFilesInBox')}
                                </h3>
                                <p className="text-gray-500">
                                    This box doesn`t contain any files yet.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </AdaptiveCard>
        </Container>
    )
}

export default BoxFiles
