import { useMutation } from '@tanstack/react-query'
import { signInApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'
import { handleAuthErrors } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export function useSignIn() {
    const { setAuth } = useAuthStore()

    return useMutation({
        mutationFn: signInApi,
        onSuccess: (data) => {
            if (data && data.token) {
                setAuth(data.token, data.refreshToken)
            }
        },
        onError: (error: AxiosError) => {
            // Use the new reusable error handler for authentication errors
            handleAuthErrors(error)
        },
    })
}
