import { checkErrors } from '@/utils/errorsUtils'
import { useMutation } from '@tanstack/react-query'
import { signInApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'
import { showCustomErrorToast } from '@/utils/globalErrorHandler'
import { AxiosError } from 'axios'
import { ErrorResponse } from '@/@types/global'

export function useSignIn() {
    const { setAuth } = useAuthStore()

    return useMutation({
        mutationFn: signInApi,
        onSuccess: (data) => {
            if (data && data.token) {
                setAuth(data.token, data.refreshToken)
            }
        },
        onError: (error: AxiosError) => {
            const errors : ErrorResponse[] | null = checkErrors(error)
            if (errors) {
                errors.forEach((error) => {
                    showCustomErrorToast(error.description || '')
                })
            }
        },
    })
}

const msg = (code : string)=>{
    return 'Invalid credentials'
}
