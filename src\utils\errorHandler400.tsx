import { ErrorResponse } from '@/@types/global'
import { AxiosError } from 'axios'
import { toast, Notification } from '@/components/ui'
import i18n from '@/locales/locales'

/**
 * Handle 400 errors with translation support
 * This utility provides a reusable way to handle 400 errors across the application
 */
export const handle400Errors = (
    error: AxiosError,
    options?: {
        duration?: number
        placement?: 'top-center' | 'top-start' | 'top-end' | 'bottom-center' | 'bottom-start' | 'bottom-end'
        customErrorMap?: Record<string, string>
        fallbackMessage?: string
    }
) => {
    const {
        duration = 3000,
        placement = 'top-center',
        customErrorMap = {},
        fallbackMessage = 'An error occurred'
    } = options || {}

    if (error.response?.status !== 400) {
        return false // Not a 400 error, let other handlers deal with it
    }

    const errors: ErrorResponse[] =
        (error.response?.data as { errors?: ErrorResponse[] })?.errors || []

    if (errors.length === 0) {
        // No specific errors array, show generic message
        showErrorToast(fallbackMessage, duration, placement)
        return true
    }

    // Handle each error in the array
    errors.forEach((errorItem) => {
        const translatedMessage = getTranslatedErrorMessage(errorItem, customErrorMap)
        showErrorToast(translatedMessage, duration, placement)
    })

    return true
}

/**
 * Get translated error message for a specific error
 */
export const getTranslatedErrorMessage = (
    error: ErrorResponse,
    customErrorMap: Record<string, string> = {}
): string => {
    const { code, description } = error

    // First check if there's a custom mapping for this specific code
    if (customErrorMap[code]) {
        return customErrorMap[code]
    }

    // Try to get translation from the errors.users section
    const userErrorKey = `errors.users.${code}`
    const userErrorTranslation = i18n.t(userErrorKey)
    
    // If translation exists and is not the same as the key (meaning it was found)
    if (userErrorTranslation && userErrorTranslation !== userErrorKey) {
        return userErrorTranslation
    }

    // Try to get translation from general errors section
    const generalErrorKey = `errors.${code}`
    const generalErrorTranslation = i18n.t(generalErrorKey)
    
    if (generalErrorTranslation && generalErrorTranslation !== generalErrorKey) {
        return generalErrorTranslation
    }

    // Fall back to the description from the server
    return description || 'Unknown error occurred'
}

/**
 * Show error toast notification
 */
const showErrorToast = (
    message: string,
    duration: number,
    placement: 'top-center' | 'top-start' | 'top-end' | 'bottom-center' | 'bottom-start' | 'bottom-end'
) => {
    toast.push(
        <Notification title="" type="danger" duration={duration}>
            {message}
        </Notification>,
        { placement }
    )
}

/**
 * Specific handler for authentication errors
 * This is a specialized version for sign-in and other auth operations
 */
export const handleAuthErrors = (error: AxiosError) => {
    return handle400Errors(error, {
        duration: 3000,
        placement: 'top-center',
        customErrorMap: {
            // You can add specific overrides here if needed
            // 'User.NotApproved': 'Custom message for not approved',
        },
        fallbackMessage: 'Authentication failed. Please try again.'
    })
}

/**
 * Generic handler that can be used in any mutation's onError
 * Usage: onError: (error) => handleGeneric400Errors(error)
 */
export const handleGeneric400Errors = (
    error: AxiosError,
    fallbackMessage: string = 'Operation failed. Please try again.'
) => {
    return handle400Errors(error, {
        fallbackMessage
    })
}
